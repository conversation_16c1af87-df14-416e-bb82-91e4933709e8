import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>t<PERSON>ontroller } from './chat.controller';
import { ChatService } from './chat.service';
import { ChatGateway } from './chat.gateway';
import { LlmModule } from '../llm/llm.module';
import { ToolCallingModule } from '../tool-calling/tool-calling.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [LlmModule, ToolCallingModule, AuthModule],
  controllers: [ChatController],
  providers: [ChatService, ChatGateway],
  exports: [ChatService],
})
export class ChatModule {}
