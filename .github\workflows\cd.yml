name: CD

on:
  push:
    branches: [main]
    tags: ["v*"]

jobs:
  build-and-push:
    name: <PERSON><PERSON> and Push Docker Images
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: your-org/otrs-ai-powered
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=ref,event=branch
            type=sha,format=short

      - name: Build and push frontend
        uses: docker/build-push-action@v4
        with:
          context: .
          file: packages/frontend/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}-frontend
          labels: ${{ steps.meta.outputs.labels }}

      - name: Build and push backend
        uses: docker/build-push-action@v4
        with:
          context: .
          file: packages/backend/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}-backend
          labels: ${{ steps.meta.outputs.labels }}

      - name: Build and push mcp-server
        uses: docker/build-push-action@v4
        with:
          context: .
          file: packages/mcp-server/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}-mcp-server
          labels: ${{ steps.meta.outputs.labels }}

      - name: Build and push rag-system
        uses: docker/build-push-action@v4
        with:
          context: .
          file: packages/rag-system/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}-rag-system
          labels: ${{ steps.meta.outputs.labels }}

  deploy:
    name: Deploy
    needs: build-and-push
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    steps:
      - uses: actions/checkout@v3

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: "latest"

      - name: Set up kubeconfig
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBE_CONFIG }}" > $HOME/.kube/config
          chmod 600 $HOME/.kube/config

      - name: Update deployment manifests
        run: |
          # Extract version from tag
          VERSION=${GITHUB_REF#refs/tags/v}

          # Update image tags in Kubernetes manifests
          sed -i "s|image: your-org/otrs-ai-powered:.*-frontend|image: your-org/otrs-ai-powered:v$VERSION-frontend|g" k8s/frontend-deployment.yaml
          sed -i "s|image: your-org/otrs-ai-powered:.*-backend|image: your-org/otrs-ai-powered:v$VERSION-backend|g" k8s/backend-deployment.yaml
          sed -i "s|image: your-org/otrs-ai-powered:.*-mcp-server|image: your-org/otrs-ai-powered:v$VERSION-mcp-server|g" k8s/mcp-server-deployment.yaml
          sed -i "s|image: your-org/otrs-ai-powered:.*-rag-system|image: your-org/otrs-ai-powered:v$VERSION-rag-system|g" k8s/rag-system-deployment.yaml

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f k8s/ -n otrs-ai

      - name: Verify deployment
        run: |
          kubectl rollout status deployment/frontend -n otrs-ai
          kubectl rollout status deployment/backend -n otrs-ai
          kubectl rollout status deployment/mcp-server -n otrs-ai
          kubectl rollout status deployment/rag-system -n otrs-ai
