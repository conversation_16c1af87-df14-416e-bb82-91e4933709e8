import express from 'express';
import dotenv from 'dotenv';
import { EmbeddingService } from './embeddings/embedding.service';
import { VectorDbService } from './storage/vector-db.service';
import { RetrieverService } from './retrieval/retriever.service';
import { IndexerService } from './indexing/indexer.service';
import { logger } from './utils/logger';

// Load environment variables
dotenv.config();

// Initialize services
const embeddingService = new EmbeddingService();
const vectorDbService = new VectorDbService();
const retrieverService = new RetrieverService(vectorDbService, embeddingService);
const indexerService = new IndexerService(vectorDbService, embeddingService);

// Create Express app
const app = express();
const port = process.env.PORT || 6000;

// Middleware
app.use(express.json());

// API endpoints
app.post('/api/query', async (req, res) => {
  try {
    const { query, topK = 5 } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }

    logger.info(`Processing query: ${query}`);
    const results = await retrieverService.retrieveRelevantDocuments(query, topK);

    return res.json({ results });
  } catch (error) {
    logger.error('Error processing query:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while processing the query';
    return res.status(500).json({
      error: errorMessage
    });
  }
});

app.post('/api/index', async (req, res) => {
  try {
    const { documents } = req.body;

    if (!documents || !Array.isArray(documents)) {
      return res.status(400).json({ error: 'Documents array is required' });
    }

    logger.info(`Indexing ${documents.length} documents`);
    const result = await indexerService.indexDocuments(documents);

    return res.json({ success: true, count: result.count });
  } catch (error) {
    logger.error('Error indexing documents:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while indexing documents';
    return res.status(500).json({
      error: errorMessage
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Start the server
if (process.env.NODE_ENV !== 'test') {
  app.listen(port, () => {
    logger.info(`RAG System running on port ${port}`);
  });
}

export {
  embeddingService,
  vectorDbService,
  retrieverService,
  indexerService
};
