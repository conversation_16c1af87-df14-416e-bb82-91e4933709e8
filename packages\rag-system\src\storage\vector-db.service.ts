import { Document, DocumentWithEmbedding } from '../types';
import { logger } from '../utils/logger';

/**
 * Service for interacting with the vector database
 */
export class VectorDbService {
  private documents: DocumentWithEmbedding[] = [];

  constructor() {
    // In a real implementation, this would initialize a connection to a vector database
    // like Pinecone, Weaviate, or Milvus
    logger.info('Initializing vector database service');
  }

  /**
   * Store documents with their embeddings in the vector database
   * @param documents Documents with embeddings to store
   * @returns Number of documents stored
   */
  async storeDocuments(documents: DocumentWithEmbedding[]): Promise<{ count: number }> {
    try {
      logger.info(`Storing ${documents.length} documents in vector database`);

      // In a real implementation, this would store the documents in the vector database
      // For now, we'll just add them to our in-memory array

      this.documents.push(...documents);

      return { count: documents.length };
    } catch (error) {
      logger.error('Error storing documents:', error);
      throw new Error(`Failed to store documents: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Search for similar documents using a query embedding
   * @param queryEmbedding Embedding of the query
   * @param topK Number of results to return
   * @returns Array of documents with similarity scores
   */
  async similaritySearch(
    queryEmbedding: number[],
    topK: number = 5
  ): Promise<Array<{ document: Document; score: number }>> {
    try {
      logger.info(`Performing similarity search with topK=${topK}`);

      // In a real implementation, this would query the vector database
      // For now, we'll compute cosine similarity with our in-memory documents

      const results = this.documents.map(doc => ({
        document: {
          id: doc.id,
          content: doc.content,
          metadata: doc.metadata,
        },
        score: this.cosineSimilarity(queryEmbedding, doc.embedding),
      }));

      // Sort by similarity score (descending) and take top K
      return results
        .sort((a, b) => b.score - a.score)
        .slice(0, topK);
    } catch (error) {
      logger.error('Error performing similarity search:', error);
      throw new Error(`Failed to perform similarity search: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Delete documents from the vector database
   * @param documentIds IDs of documents to delete
   * @returns Number of documents deleted
   */
  async deleteDocuments(documentIds: string[]): Promise<{ count: number }> {
    try {
      logger.info(`Deleting ${documentIds.length} documents from vector database`);

      // In a real implementation, this would delete the documents from the vector database
      // For now, we'll just filter our in-memory array

      const initialCount = this.documents.length;
      this.documents = this.documents.filter(doc => !documentIds.includes(doc.id));
      const deletedCount = initialCount - this.documents.length;

      return { count: deletedCount };
    } catch (error) {
      logger.error('Error deleting documents:', error);
      throw new Error(`Failed to delete documents: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   * @param a First vector
   * @param b Second vector
   * @returns Cosine similarity score
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same dimensions');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (normA * normB);
  }
}
