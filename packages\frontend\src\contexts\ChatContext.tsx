import React, { createContext, useContext, useState, useEffect } from 'react';
import { io } from 'socket.io-client';
import { ChatMessage, ChatSession, WEBSOCKET_EVENTS } from '@otrs-ai-powered/shared';
import { chatService } from '../services/chatService';
import { useAuth } from './AuthContext';

interface ChatContextType {
  messages: ChatMessage[];
  isTyping: boolean;
  isLoading: boolean;
  currentSession: ChatSession | null;
  sendMessage: (content: string) => Promise<void>;
  createSession: () => Promise<ChatSession>;
  loadSession: (sessionId: string) => Promise<ChatSession>;
  clearMessages: () => void;
}

const ChatContext = createContext<ChatContextType>({
  messages: [],
  isTyping: false,
  isLoading: false,
  currentSession: null,
  sendMessage: async () => {},
  createSession: async () => ({} as ChatSession),
  loadSession: async () => ({} as ChatSession),
  clearMessages: () => {},
});

export const useChat = () => useContext(ChatContext);

export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);

  // Initialize WebSocket connection
  useEffect(() => {
    if (isAuthenticated && user) {
      const token = localStorage.getItem('accessToken');
      console.log(`[ChatContext] Initializing WebSocket connection for user: ${user.username}`);
      console.log(`[ChatContext] Token for WebSocket: ${token ? 'present' : 'missing'}`);

      const newSocket = io({
        auth: {
          token,
        },
      });

      newSocket.on(WEBSOCKET_EVENTS.CONNECT, () => {
        console.log('[ChatContext] Connected to WebSocket server');
      });

      newSocket.on(WEBSOCKET_EVENTS.DISCONNECT, () => {
        console.log('[ChatContext] Disconnected from WebSocket server');
      });

      newSocket.on(WEBSOCKET_EVENTS.MESSAGE, (message: ChatMessage) => {
        console.log('[ChatContext] Received message via WebSocket:', message);
        setMessages((prevMessages) => [...prevMessages, message]);
        setIsTyping(false);
      });

      newSocket.on(WEBSOCKET_EVENTS.TYPING, () => {
        console.log('[ChatContext] Received typing event via WebSocket');
        setIsTyping(true);
      });

      newSocket.on(WEBSOCKET_EVENTS.ERROR, (error: any) => {
        console.error('[ChatContext] WebSocket error:', error);
      });

      newSocket.on('connect_error', (error: any) => {
        console.error('[ChatContext] WebSocket connection error:', error);
      });

      return () => {
        console.log('[ChatContext] Disconnecting WebSocket');
        newSocket.disconnect();
      };
    } else {
      console.log('[ChatContext] Not initializing WebSocket - user not authenticated');
    }
  }, [isAuthenticated, user]);

  const sendMessage = async (content: string) => {
    if (!isAuthenticated || !currentSession) {
      throw new Error('Not authenticated or no active session');
    }

    const newMessage: Partial<ChatMessage> = {
      content,
      role: 'user',
      timestamp: new Date().toISOString(),
    };

    try {
      // Add message to UI immediately
      const tempId = `temp-${Date.now()}`;
      setMessages((prevMessages) => [
        ...prevMessages,
        { ...newMessage, id: tempId } as ChatMessage,
      ]);

      // Send message to server
      const sentMessage = await chatService.sendMessage(currentSession.id, content);

      // Replace temp message with actual message
      setMessages((prevMessages) =>
        prevMessages.map((msg) => (msg.id === tempId ? sentMessage : msg))
      );
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  };

  const createSession = async () => {
    if (!isAuthenticated) {
      throw new Error('Not authenticated');
    }

    setIsLoading(true);
    try {
      const session = await chatService.createSession();
      setCurrentSession(session);
      setMessages([]);
      return session;
    } catch (error) {
      console.error('Failed to create session:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loadSession = async (sessionId: string) => {
    if (!isAuthenticated) {
      throw new Error('Not authenticated');
    }

    setIsLoading(true);
    try {
      const session = await chatService.getSession(sessionId);
      setCurrentSession(session);
      setMessages(session.messages);
      return session;
    } catch (error) {
      console.error('Failed to load session:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  return (
    <ChatContext.Provider
      value={{
        messages,
        isTyping,
        isLoading,
        currentSession,
        sendMessage,
        createSession,
        loadSession,
        clearMessages,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};
